use solana_sdk::signature::{Keypair, Signer};
use bip39::{Mnemonic, Language};
use ed25519_dalek_bip32::ExtendedSecretKey;

// Same derivation function as in main.rs
fn derive_keypair_from_mnemonic(mnemonic: &Mnemonic) -> Keypair {
    let seed = mnemonic.to_seed("");
    let extended_secret_key = ExtendedSecretKey::from_seed(&seed).unwrap();

    let derived_key = extended_secret_key
        .derive(&[
            ed25519_dalek_bip32::ChildIndex::Hardened(44),  // Purpose: BIP44
            ed25519_dalek_bip32::ChildIndex::Hardened(501), // Coin type: Solana
            ed25519_dalek_bip32::ChildIndex::Hardened(0),   // Account: 0
            ed25519_dalek_bip32::ChildIndex::Hardened(0),   // Change: 0
        ])
        .unwrap();

    let secret_key_bytes = derived_key.secret_key.to_bytes();
    Keypair::from_bytes(&[&secret_key_bytes[..], &derived_key.public_key().to_bytes()[..]].concat()).unwrap()
}

fn main() {
    // Test with the mnemonic from the latest run
    let mnemonic_str = "ocean own disagree snow feed pond exist flush ranch enforce middle vehicle";
    let expected_pubkey = "AyhV8KdV4zqiVo7eBmAgVLJRwwqs4VcFEFh65FdeUaXy";

    // Parse the mnemonic
    let mnemonic = Mnemonic::parse_in_normalized(Language::English, mnemonic_str).unwrap();

    // Derive keypair using the same method as in main.rs
    let keypair = derive_keypair_from_mnemonic(&mnemonic);
    let derived_pubkey = keypair.pubkey().to_string();

    println!("Mnemonic: {}", mnemonic_str);
    println!("Expected Public Key: {}", expected_pubkey);
    println!("Derived Public Key:  {}", derived_pubkey);
    println!("Match: {}", expected_pubkey == derived_pubkey);

    println!("\nNow you can test this mnemonic in Phantom wallet:");
    println!("1. Import wallet using the 12-word phrase above");
    println!("2. Check if the public key matches: {}", expected_pubkey);
}
