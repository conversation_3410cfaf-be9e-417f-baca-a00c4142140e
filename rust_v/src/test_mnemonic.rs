use solana_sdk::signature::{Keypair, Signer, SeedDerivable};
use bip39::{Mnemonic, Language};

fn main() {
    // Test with the mnemonic from the previous run
    let mnemonic_str = "release tornado ranch insect insane diagram narrow wave stamp vicious finger lounge";
    let expected_pubkey = "ACC1WfMaDHk6sp6nuybhE65xexaBqeJHrEvk5MuNZ4it";
    
    // Parse the mnemonic
    let mnemonic = Mnemonic::parse_in_normalized(Language::English, mnemonic_str).unwrap();
    
    // Derive keypair using the same method as in main.rs
    let seed = mnemonic.to_seed("");
    let keypair = Keypair::from_seed(&seed[..32]).unwrap();
    let derived_pubkey = keypair.pubkey().to_string();
    
    println!("Mnemonic: {}", mnemonic_str);
    println!("Expected Public Key: {}", expected_pubkey);
    println!("Derived Public Key:  {}", derived_pubkey);
    println!("Match: {}", expected_pubkey == derived_pubkey);
}
