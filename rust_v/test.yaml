
# Loads default set of integrations. Do not remove.
default_config:

# Load frontend themes from the themes folder
frontend:
  themes: !include_dir_merge_named themes

automation: !include automations.yaml
script: !include scripts.yaml
scene: !include scenes.yaml
template: !include templates.yaml

lovelace:
  mode: storage
  resources:
    - url: /hacsfiles/decluttering-card/decluttering-card.js
      type: module

decluttering_templates: !include custom_cards/decluttering_templates.yaml

sensor:
  - platform: integration
    source: sensor.total_power
    name: Total Energy
    unit_prefix: k
    round: 3
    method: trapezoidal
    unit_time: h

utility_meter:
  total_energy_hourly:
    source: sensor.total_energy
    cycle: hourly
  total_energy_daily:
    source: sensor.total_energy
    cycle: daily
  total_energy_weekly:
    source: sensor.total_energy
    cycle: weekly
  total_energy_monthly:
    source: sensor.total_energy
    cycle: monthly
  total_energy_yearly:
    source: sensor.total_energy
    cycle: yearly

input_select:
  energy_view_mode:
    name: Energy View Range
    options:
      - Hourly
      - Daily
      - Weekly
      - Monthly
      - Yearly
    initial: Daily

