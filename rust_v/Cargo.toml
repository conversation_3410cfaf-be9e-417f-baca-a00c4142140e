[package]
name = "solana_vanity_generator"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "test_mnemonic"
path = "src/test_mnemonic.rs"

[dependencies]
solana-sdk = "1.18.0"
bs58 = "0.4"
rayon = "1.5"
num_cpus = "1.16"
parking_lot = "0.12"
bip39 = "2.0"
rand = "0.8"
hmac = "0.12"
sha2 = "0.10"
pbkdf2 = "0.12"
ed25519-dalek-bip32 = "0.2"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
